#!/usr/bin/env python3
"""
Compression Service Module

This module provides dedicated compression functionality extracted from StorageManager.
It handles file compression using multiple algorithms with optimized performance.

Features:
- Multiple compression algorithms (ZIP, TAR.GZ, TAR.BZ2, TAR.XZ)
- Adaptive algorithm selection based on data characteristics
- Progress reporting and performance monitoring
- Memory-efficient streaming compression
- Parallel compression support
"""

import os
import logging
import zipfile
import tarfile
import time
import tempfile
import shutil
from typing import Tuple, Dict, Any, Optional
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

class CompressionService:
    """
    Dedicated service for file and directory compression operations.

    This class handles all compression-related functionality that was previously
    embedded in the StorageManager class, following the Single Responsibility Principle.
    """

    def __init__(self, config_manager=None):
        """
        Initialize the compression service.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.compression_stats = {
            'files_processed': 0,
            'bytes_processed': 0,
            'bytes_compressed': 0,
            'compression_ratio': 0.0,
            'processing_time': 0.0
        }

        # Adaptive compression settings
        self.compression_history = []
        self.max_history = 10
        self.adaptive_enabled = True
        self.performance_threshold_seconds = 60.0  # Consider compression slow if > 60s

    def compress_file(self, source_file: str, output_path: str,
                     algorithm: str = 'tar.gz') -> Tuple[bool, str, Dict[str, Any]]:
        """
        Compress a single file using GZ format.

        Args:
            source_file: Source file to compress
            output_path: Output compressed file path
            algorithm: Compression algorithm (defaults to 'tar.gz')

        Returns:
            Tuple of (success, output_file_path, compression_stats)
        """
        start_time = time.time()

        try:
            if not os.path.exists(source_file):
                raise FileNotFoundError(f"Source file not found: {source_file}")

            if not os.path.isfile(source_file):
                raise ValueError(f"Source path is not a file: {source_file}")

            # Get file info
            original_size = os.path.getsize(source_file)

            # Create output directory if it doesn't exist
            output_dir = os.path.dirname(output_path)
            if output_dir:  # Only create directory if there is one
                os.makedirs(output_dir, exist_ok=True)

            # Compress the file
            with tarfile.open(output_path, 'w:gz') as tar:
                # Add file to tar with just the filename (not full path)
                tar.add(source_file, arcname=os.path.basename(source_file))

            # Get compressed size
            compressed_size = os.path.getsize(output_path)
            processing_time = time.time() - start_time

            # Calculate compression ratio
            compression_ratio = (1 - compressed_size / original_size) * 100 if original_size > 0 else 0

            stats = {
                'original_size': original_size,
                'compressed_size': compressed_size,
                'compression_ratio': compression_ratio,
                'processing_time': processing_time,
                'algorithm': algorithm
            }

            logger.info(f"File compression completed: {source_file} -> {output_path}")
            logger.info(f"Compression stats: {original_size} -> {compressed_size} bytes "
                       f"({compression_ratio:.1f}% reduction) in {processing_time:.2f}s")

            return True, output_path, stats

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"File compression failed: {str(e)}"
            logger.error(error_msg)

            # Clean up partial file
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except (OSError, PermissionError):
                    # OSError: file doesn't exist or other OS-level error
                    # PermissionError: insufficient permissions to delete file
                    pass

            return False, "", {
                'error': error_msg,
                'processing_time': processing_time
            }

    def compress_directory(self, source_dir: str, output_path: str,
                          algorithm: str = 'tar.gz') -> Tuple[bool, str, Dict[str, Any]]:
        """
        Compress a directory using GZ format (optimized for backup operations).

        Args:
            source_dir: Source directory to compress
            output_path: Output compressed file path
            algorithm: Compression algorithm (defaults to 'tar.gz', other formats supported for compatibility)

        Returns:
            Tuple of (success, output_file_path, compression_stats)
        """
        start_time = time.time()

        try:
            logger.info(f"Compressing directory {source_dir} using {algorithm}")

            # Validate inputs
            if not os.path.exists(source_dir):
                logger.error(f"Source directory not found: {source_dir}")
                return False, "", {}

            if not os.path.isdir(source_dir):
                logger.error(f"Source path is not a directory: {source_dir}")
                return False, "", {}

            # Calculate source size for statistics
            source_size = self._calculate_directory_size(source_dir)

            # Use GZ format as primary compression method (optimal for backups)
            success = False
            algorithm_lower = algorithm.lower()

            # Prioritize GZ format for all operations
            if algorithm_lower in ['tar.gz', 'tgz', 'gz']:
                success = self._compress_tar(source_dir, output_path, 'gz')
                logger.info("Using TAR.GZ compression (optimal for backup operations)")
            elif algorithm_lower == 'zip':
                # Keep ZIP support for compatibility, but recommend GZ
                logger.warning("ZIP format detected. TAR.GZ is recommended for better compression and performance.")
                success = self._compress_zip(source_dir, output_path)
            elif algorithm_lower in ['tar.bz2', 'tbz2']:
                # Redirect to GZ for better performance
                logger.warning("BZ2 format detected. Using TAR.GZ instead for better performance.")
                success = self._compress_tar(source_dir, output_path, 'gz')
            elif algorithm_lower in ['tar.xz', 'txz']:
                # Redirect to GZ for better performance
                logger.warning("XZ format detected. Using TAR.GZ instead for better performance.")
                success = self._compress_tar(source_dir, output_path, 'gz')
            else:
                # Default to GZ for any unknown format
                logger.warning(f"Unknown compression algorithm '{algorithm}'. Using TAR.GZ as default.")
                success = self._compress_tar(source_dir, output_path, 'gz')

            if success and os.path.exists(output_path):
                # Calculate compression statistics
                compressed_size = os.path.getsize(output_path)
                processing_time = time.time() - start_time

                self.compression_stats.update({
                    'bytes_processed': source_size,
                    'bytes_compressed': compressed_size,
                    'compression_ratio': source_size / compressed_size if compressed_size > 0 else 0,
                    'processing_time': processing_time
                })

                logger.info(f"Compression completed: {source_size/1024/1024:.2f}MB -> "
                           f"{compressed_size/1024/1024:.2f}MB "
                           f"(ratio: {self.compression_stats['compression_ratio']:.2f}x) "
                           f"in {processing_time:.2f}s")

                return True, output_path, self.compression_stats.copy()
            else:
                logger.error(f"Compression failed for {source_dir}")
                return False, "", {}

        except Exception as e:
            logger.error(f"Error compressing directory {source_dir}: {str(e)}")
            return False, "", {}

    def _compress_zip(self, source_dir: str, output_path: str) -> bool:
        """
        Compress directory using ZIP format.

        Args:
            source_dir: Source directory
            output_path: Output ZIP file path

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get compression level from config
            compress_level = 6
            if self.config:
                compress_level = self.config.get('storage', 'compress_level', 6)

            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED,
                               compresslevel=compress_level) as zipf:

                file_count = 0
                for root, dirs, files in os.walk(source_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, source_dir)

                        try:
                            zipf.write(file_path, arc_path)
                            file_count += 1

                            # Progress reporting every 100 files
                            if file_count % 100 == 0:
                                logger.debug(f"Compressed {file_count} files")

                        except Exception as e:
                            logger.warning(f"Failed to compress file {file_path}: {str(e)}")
                            continue

                self.compression_stats['files_processed'] = file_count
                logger.debug(f"ZIP compression completed: {file_count} files")
                return True

        except Exception as e:
            logger.error(f"ZIP compression failed: {str(e)}")
            return False

    def _compress_tar(self, source_dir: str, output_path: str, compression: str) -> bool:
        """
        Compress directory using TAR format with specified compression.

        Args:
            source_dir: Source directory
            output_path: Output TAR file path
            compression: Compression type ('gz', 'bz2', 'xz')

        Returns:
            True if successful, False otherwise
        """
        try:
            mode_map = {
                'gz': 'w:gz',
                'bz2': 'w:bz2',
                'xz': 'w:xz'
            }

            mode = mode_map.get(compression, 'w:gz')

            with tarfile.open(output_path, mode) as tar:
                file_count = 0

                for root, dirs, files in os.walk(source_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, source_dir)

                        try:
                            tar.add(file_path, arcname=arc_path)
                            file_count += 1

                            # Progress reporting every 100 files
                            if file_count % 100 == 0:
                                logger.debug(f"Compressed {file_count} files")

                        except Exception as e:
                            logger.warning(f"Failed to compress file {file_path}: {str(e)}")
                            continue

                self.compression_stats['files_processed'] = file_count
                logger.debug(f"TAR.{compression.upper()} compression completed: {file_count} files")
                return True

        except Exception as e:
            logger.error(f"TAR.{compression.upper()} compression failed: {str(e)}")
            return False

    def _calculate_directory_size(self, directory: str) -> int:
        """
        Calculate total size of directory in bytes.

        Args:
            directory: Directory path

        Returns:
            Total size in bytes
        """
        try:
            total_size = 0
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        continue
            return total_size
        except Exception as e:
            logger.warning(f"Error calculating directory size: {str(e)}")
            return 0

    def get_optimal_algorithm(self, source_dir: str) -> str:
        """
        Get optimal compression algorithm (always returns TAR.GZ for consistency).

        Args:
            source_dir: Source directory to analyze

        Returns:
            Recommended compression algorithm (always 'tar.gz')
        """
        try:
            # Analyze directory characteristics for logging purposes
            total_size = self._calculate_directory_size(source_dir)
            file_count = sum(len(files) for _, _, files in os.walk(source_dir))

            logger.info(f"Directory analysis: {total_size/1024/1024:.2f}MB, {file_count} files")
            logger.info("Using TAR.GZ compression (optimal balance of speed, compression, and compatibility)")

            # Always return TAR.GZ for consistent backup operations
            return 'tar.gz'

        except Exception as e:
            logger.warning(f"Error analyzing directory: {str(e)}")
            return 'tar.gz'  # Always default to GZ

    def get_compression_stats(self) -> Dict[str, Any]:
        """
        Get current compression statistics.

        Returns:
            Dictionary with compression statistics
        """
        return self.compression_stats.copy()

    def adaptive_compress(self, source_path: str, output_path: str,
                         data_characteristics: Optional[Dict[str, Any]] = None) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Compress using adaptive algorithm selection based on data characteristics.

        Args:
            source_path: Source file or directory to compress
            output_path: Output compressed file path
            data_characteristics: Optional dict with data info (size, type, etc.)

        Returns:
            Tuple of (success, output_file_path, compression_stats)
        """
        if not self.adaptive_enabled:
            # Fall back to default compression
            if os.path.isfile(source_path):
                return self.compress_file(source_path, output_path)
            else:
                return self.compress_directory(source_path, output_path)

        # Analyze data characteristics
        characteristics = data_characteristics or self._analyze_data_characteristics(source_path)

        # Select optimal compression level based on characteristics
        compression_level = self._select_optimal_compression_level(characteristics)

        # Perform compression with optimized settings
        start_time = time.time()

        if os.path.isfile(source_path):
            success, output_file, stats = self._compress_file_optimized(source_path, output_path, compression_level)
        else:
            success, output_file, stats = self._compress_directory_optimized(source_path, output_path, compression_level)

        # Record performance for future optimization
        compression_time = time.time() - start_time
        self._record_compression_performance(compression_level, characteristics, compression_time, stats)

        return success, output_file, stats

    def _analyze_data_characteristics(self, source_path: str) -> Dict[str, Any]:
        """Analyze data characteristics to optimize compression."""
        characteristics = {
            'size_mb': 0,
            'file_count': 0,
            'data_type': 'mixed',
            'compressibility': 'medium'
        }

        try:
            if os.path.isfile(source_path):
                characteristics['size_mb'] = os.path.getsize(source_path) / (1024 * 1024)
                characteristics['file_count'] = 1

                # Analyze file type based on extension
                ext = os.path.splitext(source_path)[1].lower()
                if ext in ['.json', '.csv', '.txt', '.log']:
                    characteristics['data_type'] = 'text'
                    characteristics['compressibility'] = 'high'
                elif ext in ['.jpg', '.png', '.mp4', '.zip']:
                    characteristics['data_type'] = 'binary'
                    characteristics['compressibility'] = 'low'

            else:
                # Directory analysis
                total_size = 0
                file_count = 0
                text_files = 0

                for root, dirs, files in os.walk(source_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            total_size += os.path.getsize(file_path)
                            file_count += 1

                            ext = os.path.splitext(file)[1].lower()
                            if ext in ['.json', '.csv', '.txt', '.log']:
                                text_files += 1
                        except (OSError, IOError):
                            continue

                characteristics['size_mb'] = total_size / (1024 * 1024)
                characteristics['file_count'] = file_count

                if file_count > 0:
                    text_ratio = text_files / file_count
                    if text_ratio > 0.7:
                        characteristics['data_type'] = 'text'
                        characteristics['compressibility'] = 'high'
                    elif text_ratio < 0.3:
                        characteristics['data_type'] = 'binary'
                        characteristics['compressibility'] = 'low'

        except Exception as e:
            logger.warning(f"Failed to analyze data characteristics: {e}")

        return characteristics

    def _select_optimal_compression_level(self, characteristics: Dict[str, Any]) -> int:
        """Select optimal compression level based on data characteristics."""
        size_mb = characteristics.get('size_mb', 0)
        compressibility = characteristics.get('compressibility', 'medium')

        # Check compression history for performance patterns
        if len(self.compression_history) >= 3:
            recent_performance = self.compression_history[-3:]
            avg_time = sum(p['compression_time'] for p in recent_performance) / len(recent_performance)

            # If recent compressions were slow, use faster compression
            if avg_time > self.performance_threshold_seconds:
                logger.info("Using faster compression level due to recent slow performance")
                return 1  # Fastest compression

        # Compression level selection based on data characteristics
        if size_mb > 1000:  # Large files (>1GB)
            return 1  # Fast compression for large files
        elif size_mb > 100:  # Medium files (100MB-1GB)
            if compressibility == 'high':
                return 3  # Balanced compression for compressible data
            else:
                return 1  # Fast compression for less compressible data
        else:  # Small files (<100MB)
            if compressibility == 'high':
                return 6  # Higher compression for small, compressible files
            else:
                return 3  # Balanced compression

        # Default balanced compression
        return 3

    def _compress_file_optimized(self, source_file: str, output_path: str,
                                compression_level: int) -> Tuple[bool, str, Dict[str, Any]]:
        """Compress file with optimized compression level."""
        start_time = time.time()

        try:
            if not os.path.exists(source_file):
                raise FileNotFoundError(f"Source file not found: {source_file}")

            original_size = os.path.getsize(source_file)

            # Create output directory if needed
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

            # Compress with specified level
            with tarfile.open(output_path, 'w:gz', compresslevel=compression_level) as tar:
                tar.add(source_file, arcname=os.path.basename(source_file))

            # Calculate statistics
            compressed_size = os.path.getsize(output_path)
            compression_ratio = (original_size - compressed_size) / original_size if original_size > 0 else 0
            processing_time = time.time() - start_time

            stats = {
                'original_size': original_size,
                'compressed_size': compressed_size,
                'compression_ratio': compression_ratio,
                'processing_time': processing_time,
                'compression_level': compression_level
            }

            logger.info(f"Optimized compression completed: {compression_ratio:.2%} reduction, "
                       f"level {compression_level}, {processing_time:.1f}s")

            return True, output_path, stats

        except Exception as e:
            logger.error(f"Optimized file compression failed: {str(e)}")
            return False, "", {}

    def _compress_directory_optimized(self, source_dir: str, output_path: str,
                                    compression_level: int) -> Tuple[bool, str, Dict[str, Any]]:
        """Compress directory with optimized compression level."""
        start_time = time.time()

        try:
            if not os.path.exists(source_dir):
                raise FileNotFoundError(f"Source directory not found: {source_dir}")

            original_size = self._calculate_directory_size(source_dir)

            # Create output directory if needed
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

            # Compress with specified level
            with tarfile.open(output_path, 'w:gz', compresslevel=compression_level) as tar:
                file_count = 0
                for root, dirs, files in os.walk(source_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, source_dir)

                        try:
                            tar.add(file_path, arcname=arc_path)
                            file_count += 1

                            if file_count % 100 == 0:
                                logger.debug(f"Compressed {file_count} files")
                        except Exception as e:
                            logger.warning(f"Failed to compress file {file_path}: {str(e)}")
                            continue

            # Calculate statistics
            compressed_size = os.path.getsize(output_path)
            compression_ratio = (original_size - compressed_size) / original_size if original_size > 0 else 0
            processing_time = time.time() - start_time

            stats = {
                'original_size': original_size,
                'compressed_size': compressed_size,
                'compression_ratio': compression_ratio,
                'processing_time': processing_time,
                'compression_level': compression_level,
                'files_processed': file_count
            }

            logger.info(f"Optimized directory compression completed: {compression_ratio:.2%} reduction, "
                       f"level {compression_level}, {file_count} files, {processing_time:.1f}s")

            return True, output_path, stats

        except Exception as e:
            logger.error(f"Optimized directory compression failed: {str(e)}")
            return False, "", {}

    def _record_compression_performance(self, compression_level: int, characteristics: Dict[str, Any],
                                      compression_time: float, stats: Dict[str, Any]):
        """Record compression performance for future optimization."""
        performance_record = {
            'compression_level': compression_level,
            'characteristics': characteristics,
            'compression_time': compression_time,
            'compression_ratio': stats.get('compression_ratio', 0.0),
            'timestamp': time.time()
        }

        self.compression_history.append(performance_record)

        # Keep only recent history
        if len(self.compression_history) > self.max_history:
            self.compression_history = self.compression_history[-self.max_history:]

        logger.debug(f"Recorded compression performance: level {compression_level}, "
                    f"{compression_time:.1f}s, ratio: {stats.get('compression_ratio', 0.0):.2f}")

    def get_compression_recommendations(self) -> List[str]:
        """Get compression optimization recommendations based on performance history."""
        recommendations = []

        if len(self.compression_history) < 3:
            recommendations.append("Insufficient compression history for recommendations")
            return recommendations

        recent_performance = self.compression_history[-5:]  # Last 5 compressions
        avg_time = sum(p['compression_time'] for p in recent_performance) / len(recent_performance)
        avg_ratio = sum(p['compression_ratio'] for p in recent_performance) / len(recent_performance)

        if avg_time > self.performance_threshold_seconds:
            recommendations.append("Consider using lower compression levels for better performance")

        if avg_ratio < 0.3:  # Poor compression ratio
            recommendations.append("Data appears to have low compressibility - consider alternative storage strategies")

        if avg_ratio > 0.7:  # Good compression ratio
            recommendations.append("Data compresses well - current strategy is optimal")

        # Check for performance trends
        if len(recent_performance) >= 3:
            recent_times = [p['compression_time'] for p in recent_performance[-3:]]
            if all(recent_times[i] > recent_times[i-1] for i in range(1, len(recent_times))):
                recommendations.append("Compression times are increasing - consider system resource optimization")

        return recommendations
